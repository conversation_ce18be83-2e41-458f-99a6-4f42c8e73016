#!/usr/bin/env python3
"""
Python implementation of ImageJ-like image stitching algorithm
Optimized for speed using OpenCV and NumPy
Author: AI Assistant
"""

import cv2
import numpy as np
import os
import re
import time
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TileInfo:
    """Information about a single tile"""
    filename: str
    x: float
    y: float
    image: Optional[np.ndarray] = None
    optimized_x: Optional[float] = None
    optimized_y: Optional[float] = None

@dataclass
class MatchResult:
    """Result of matching two tiles"""
    tile1_idx: int
    tile2_idx: int
    offset_x: float
    offset_y: float
    correlation: float
    valid: bool = True

class FastImageStitcher:
    """Fast image stitching implementation inspired by ImageJ"""
    
    def __init__(self, 
                 regression_threshold: float = 0.3,
                 max_displacement_threshold: float = 2.5,
                 absolute_displacement_threshold: float = 3.5,
                 subpixel_accuracy: bool = True,
                 num_threads: int = None):
        
        self.regression_threshold = regression_threshold
        self.max_displacement_threshold = max_displacement_threshold
        self.absolute_displacement_threshold = absolute_displacement_threshold
        self.subpixel_accuracy = subpixel_accuracy
        self.num_threads = num_threads or min(8, os.cpu_count())
        
        self.tiles: List[TileInfo] = []
        self.matches: List[MatchResult] = []
        
    def load_tile_configuration(self, config_path: str) -> None:
        """Load tile configuration from TileConfiguration.txt"""
        logger.info(f"Loading tile configuration from {config_path}")
        
        with open(config_path, 'r') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if line.startswith('#') or not line or line.startswith('dim'):
                continue
                
            # Parse line: filename ; ; (x,y)
            match = re.match(r'(\S+)\s*;\s*;\s*\(([^,]+),([^)]+)\)', line)
            if match:
                filename = match.group(1)
                x = float(match.group(2))
                y = float(match.group(3))
                self.tiles.append(TileInfo(filename, x, y))
        
        logger.info(f"Loaded {len(self.tiles)} tiles")
    
    def load_images(self, image_dir: str, max_workers: int = None, color_mode: str = 'color') -> None:
        """Load all images in parallel

        Args:
            image_dir: Directory containing images
            max_workers: Number of parallel workers
            color_mode: 'color' for RGB, 'grayscale' for grayscale processing
        """
        logger.info(f"Loading images in {color_mode} mode...")
        start_time = time.time()

        def load_single_image(tile_info: TileInfo) -> TileInfo:
            image_path = os.path.join(image_dir, tile_info.filename)
            if os.path.exists(image_path):
                if color_mode == 'color':
                    # Load as color image (BGR format)
                    img = cv2.imread(image_path, cv2.IMREAD_COLOR)
                else:
                    # Load as grayscale for faster processing
                    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)

                if img is not None:
                    tile_info.image = img
                    return tile_info
            logger.warning(f"Could not load image: {image_path}")
            return tile_info
        
        max_workers = max_workers or self.num_threads
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(load_single_image, tile) for tile in self.tiles]
            
            for i, future in enumerate(as_completed(futures)):
                self.tiles[i] = future.result()
                if (i + 1) % 20 == 0:
                    logger.info(f"Loaded {i + 1}/{len(self.tiles)} images")
        
        # Filter out tiles without images
        self.tiles = [tile for tile in self.tiles if tile.image is not None]
        logger.info(f"Successfully loaded {len(self.tiles)} images in {time.time() - start_time:.2f}s")
    
    def find_overlapping_pairs(self) -> List[Tuple[int, int]]:
        """Find pairs of tiles that potentially overlap"""
        pairs = []
        
        for i in range(len(self.tiles)):
            for j in range(i + 1, len(self.tiles)):
                tile1, tile2 = self.tiles[i], self.tiles[j]
                
                # Estimate image size (assuming all images have same size)
                h, w = tile1.image.shape[:2]
                
                # Check if tiles overlap based on their positions
                x1_min, y1_min = tile1.x, tile1.y
                x1_max, y1_max = tile1.x + w, tile1.y + h
                x2_min, y2_min = tile2.x, tile2.y
                x2_max, y2_max = tile2.x + w, tile2.y + h
                
                # Check for overlap
                if (x1_min < x2_max and x1_max > x2_min and 
                    y1_min < y2_max and y1_max > y2_min):
                    pairs.append((i, j))
        
        logger.info(f"Found {len(pairs)} potentially overlapping pairs")
        return pairs
    
    def compute_phase_correlation(self, img1: np.ndarray, img2: np.ndarray,
                                roi1: Tuple[int, int, int, int] = None,
                                roi2: Tuple[int, int, int, int] = None) -> Tuple[float, float, float]:
        """Compute phase correlation between two images"""

        # Extract ROIs if specified
        if roi1:
            x, y, w, h = roi1
            img1 = img1[y:y+h, x:x+w]
        if roi2:
            x, y, w, h = roi2
            img2 = img2[y:y+h, x:x+w]

        # Ensure images have the same size
        min_h = min(img1.shape[0], img2.shape[0])
        min_w = min(img1.shape[1], img2.shape[1])
        img1 = img1[:min_h, :min_w]
        img2 = img2[:min_h, :min_w]

        # Convert to grayscale if color images for phase correlation
        if len(img1.shape) == 3:
            img1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        if len(img2.shape) == 3:
            img2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # Convert to float32 for better precision
        img1 = img1.astype(np.float32)
        img2 = img2.astype(np.float32)
        
        # Apply window function to reduce edge effects
        window = np.outer(np.hanning(min_h), np.hanning(min_w))
        img1 *= window
        img2 *= window
        
        # Compute FFT
        f1 = np.fft.fft2(img1)
        f2 = np.fft.fft2(img2)
        
        # Compute cross-power spectrum
        cross_power = f1 * np.conj(f2)
        cross_power /= (np.abs(cross_power) + 1e-10)
        
        # Compute phase correlation
        correlation = np.fft.ifft2(cross_power)
        correlation = np.abs(correlation)
        
        # Find peak
        peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
        peak_value = correlation[peak_y, peak_x]
        
        # Convert to displacement (handle wraparound)
        if peak_x > min_w // 2:
            peak_x -= min_w
        if peak_y > min_h // 2:
            peak_y -= min_h
        
        # Subpixel refinement using parabolic interpolation
        if self.subpixel_accuracy and peak_value > 0.1:
            try:
                # Get neighboring values for subpixel estimation
                y_coords = [(peak_y - 1) % min_h, peak_y, (peak_y + 1) % min_h]
                x_coords = [(peak_x - 1) % min_w, peak_x, (peak_x + 1) % min_w]
                
                # Parabolic interpolation in x direction
                if len(set(x_coords)) == 3:  # Ensure we have 3 different coordinates
                    x_vals = [correlation[peak_y, x] for x in x_coords]
                    if x_vals[0] > 0 and x_vals[2] > 0:
                        dx = 0.5 * (x_vals[0] - x_vals[2]) / (x_vals[0] - 2*x_vals[1] + x_vals[2])
                        peak_x += dx
                
                # Parabolic interpolation in y direction
                if len(set(y_coords)) == 3:  # Ensure we have 3 different coordinates
                    y_vals = [correlation[y, peak_x] for y in y_coords]
                    if y_vals[0] > 0 and y_vals[2] > 0:
                        dy = 0.5 * (y_vals[0] - y_vals[2]) / (y_vals[0] - 2*y_vals[1] + y_vals[2])
                        peak_y += dy
            except:
                pass  # Fall back to integer precision
        
        return float(peak_x), float(peak_y), float(peak_value)

    def compute_roi_overlap(self, tile1: TileInfo, tile2: TileInfo) -> Tuple[Tuple[int, int, int, int], Tuple[int, int, int, int]]:
        """Compute overlapping ROIs for two tiles"""
        h, w = tile1.image.shape[:2]

        # Calculate overlap region
        x1_min, y1_min = tile1.x, tile1.y
        x1_max, y1_max = tile1.x + w, tile1.y + h
        x2_min, y2_min = tile2.x, tile2.y
        x2_max, y2_max = tile2.x + w, tile2.y + h

        # Find intersection
        overlap_x_min = max(x1_min, x2_min)
        overlap_y_min = max(y1_min, y2_min)
        overlap_x_max = min(x1_max, x2_max)
        overlap_y_max = min(y1_max, y2_max)

        # Convert to ROI coordinates relative to each image
        roi1_x = int(overlap_x_min - x1_min)
        roi1_y = int(overlap_y_min - y1_min)
        roi1_w = int(overlap_x_max - overlap_x_min)
        roi1_h = int(overlap_y_max - overlap_y_min)

        roi2_x = int(overlap_x_min - x2_min)
        roi2_y = int(overlap_y_min - y2_min)
        roi2_w = int(overlap_x_max - overlap_x_min)
        roi2_h = int(overlap_y_max - overlap_y_min)

        return (roi1_x, roi1_y, roi1_w, roi1_h), (roi2_x, roi2_y, roi2_w, roi2_h)

    def match_tile_pair(self, tile1_idx: int, tile2_idx: int) -> MatchResult:
        """Match a pair of tiles using phase correlation"""
        tile1, tile2 = self.tiles[tile1_idx], self.tiles[tile2_idx]

        # Compute overlapping ROIs
        roi1, roi2 = self.compute_roi_overlap(tile1, tile2)

        # Skip if no meaningful overlap
        if roi1[2] < 50 or roi1[3] < 50:
            return MatchResult(tile1_idx, tile2_idx, 0, 0, 0, False)

        # Compute phase correlation
        start_time = time.time()
        offset_x, offset_y, correlation = self.compute_phase_correlation(
            tile1.image, tile2.image, roi1, roi2)

        # Adjust offset based on ROI positions
        offset_x += roi1[0] - roi2[0]
        offset_y += roi1[1] - roi2[1]

        # Create match result
        result = MatchResult(tile1_idx, tile2_idx, offset_x, offset_y, correlation)

        # Validate match based on correlation threshold
        if correlation < self.regression_threshold:
            result.valid = False

        elapsed = time.time() - start_time
        logger.info(f"{tile1.filename} <- {tile2.filename}: ({offset_x:.3f}, {offset_y:.3f}) "
                   f"correlation (R)={correlation:.6f} ({elapsed*1000:.0f} ms)")

        return result

    def compute_all_matches(self) -> None:
        """Compute matches for all overlapping pairs"""
        logger.info("Computing pairwise matches...")
        start_time = time.time()

        pairs = self.find_overlapping_pairs()

        def match_wrapper(pair):
            return self.match_tile_pair(pair[0], pair[1])

        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            futures = [executor.submit(match_wrapper, pair) for pair in pairs]

            for i, future in enumerate(as_completed(futures)):
                match_result = future.result()
                if match_result.valid:
                    self.matches.append(match_result)

                if (i + 1) % 50 == 0:
                    logger.info(f"Processed {i + 1}/{len(pairs)} pairs")

        logger.info(f"Found {len(self.matches)} valid matches in {time.time() - start_time:.2f}s")

    def global_optimization(self, max_iterations: int = 10) -> None:
        """Perform global optimization of tile positions"""
        logger.info("Performing global optimization...")

        if not self.matches:
            logger.warning("No matches found for optimization")
            return

        # Initialize optimized positions with original positions
        for tile in self.tiles:
            tile.optimized_x = tile.x
            tile.optimized_y = tile.y

        # Iterative optimization
        for iteration in range(max_iterations):
            logger.info(f"Optimization iteration {iteration + 1}/{max_iterations}")

            # Build system of equations Ax = b
            n_tiles = len(self.tiles)
            n_matches = len([m for m in self.matches if m.valid])

            if n_matches == 0:
                break

            # Create coefficient matrix and right-hand side
            A = np.zeros((2 * n_matches, 2 * n_tiles))
            b = np.zeros(2 * n_matches)

            eq_idx = 0
            total_error = 0

            for match in self.matches:
                if not match.valid:
                    continue

                i, j = match.tile1_idx, match.tile2_idx

                # Expected relative position based on current optimization
                expected_dx = self.tiles[j].optimized_x - self.tiles[i].optimized_x
                expected_dy = self.tiles[j].optimized_y - self.tiles[i].optimized_y

                # Observed relative position from phase correlation
                observed_dx = match.offset_x
                observed_dy = match.offset_y

                # Error
                error_x = observed_dx - expected_dx
                error_y = observed_dy - expected_dy
                total_error += error_x**2 + error_y**2

                # Weight by correlation quality
                weight = match.correlation

                # X equation: x_j - x_i = observed_dx
                A[eq_idx, 2*i] = -weight      # -x_i
                A[eq_idx, 2*j] = weight       # +x_j
                b[eq_idx] = weight * observed_dx

                # Y equation: y_j - y_i = observed_dy
                A[eq_idx + 1, 2*i + 1] = -weight  # -y_i
                A[eq_idx + 1, 2*j + 1] = weight   # +y_j
                b[eq_idx + 1] = weight * observed_dy

                eq_idx += 2

            # Fix first tile at origin to avoid singular system
            A = np.vstack([A, np.eye(2, 2 * n_tiles)])
            b = np.hstack([b, [0, 0]])

            # Solve least squares
            try:
                solution = np.linalg.lstsq(A, b, rcond=None)[0]

                # Update positions
                for i, tile in enumerate(self.tiles):
                    tile.optimized_x = solution[2*i]
                    tile.optimized_y = solution[2*i + 1]

                avg_error = np.sqrt(total_error / n_matches)
                logger.info(f"Average error: {avg_error:.3f}")

                # Check for convergence
                if avg_error < 1.0:
                    logger.info(f"Converged after {iteration + 1} iterations")
                    break

            except np.linalg.LinAlgError:
                logger.warning("Optimization failed - using original positions")
                break

        # Remove outlier matches based on final error
        self.remove_outlier_matches()

    def remove_outlier_matches(self) -> None:
        """Remove matches with large displacement errors"""
        if not self.matches:
            return

        errors = []
        for match in self.matches:
            if not match.valid:
                continue

            i, j = match.tile1_idx, match.tile2_idx
            expected_dx = self.tiles[j].optimized_x - self.tiles[i].optimized_x
            expected_dy = self.tiles[j].optimized_y - self.tiles[i].optimized_y

            error = np.sqrt((match.offset_x - expected_dx)**2 + (match.offset_y - expected_dy)**2)
            errors.append(error)

        if not errors:
            return

        avg_error = np.mean(errors)

        # Remove matches with error > threshold
        threshold = max(avg_error * self.max_displacement_threshold, self.absolute_displacement_threshold)

        removed_count = 0
        for i, match in enumerate(self.matches):
            if not match.valid:
                continue

            if i < len(errors) and errors[i] > threshold:
                match.valid = False
                removed_count += 1

        if removed_count > 0:
            logger.info(f"Removed {removed_count} outlier matches (threshold: {threshold:.2f})")

    def create_stitched_image(self, output_path: str = None,
                            fusion_method: str = 'multiband_blending',
                            downsample_factor: int = 1) -> np.ndarray:
        """Create the final stitched image"""
        logger.info("Creating stitched image...")
        start_time = time.time()

        if not self.tiles:
            raise ValueError("No tiles loaded")

        # Calculate output image bounds
        min_x = min(tile.optimized_x or tile.x for tile in self.tiles)
        min_y = min(tile.optimized_y or tile.y for tile in self.tiles)
        max_x = max((tile.optimized_x or tile.x) + tile.image.shape[1] for tile in self.tiles)
        max_y = max((tile.optimized_y or tile.y) + tile.image.shape[0] for tile in self.tiles)

        # Apply downsampling
        output_width = int((max_x - min_x) / downsample_factor)
        output_height = int((max_y - min_y) / downsample_factor)

        logger.info(f"Output image size: {output_width} x {output_height}")

        # Create output image (3 channels for color)
        if len(self.tiles[0].image.shape) == 3:
            output_image = np.zeros((output_height, output_width, 3), dtype=np.float32)
            weight_map = np.zeros((output_height, output_width), dtype=np.float32)
        else:
            output_image = np.zeros((output_height, output_width), dtype=np.float32)
            weight_map = np.zeros((output_height, output_width), dtype=np.float32)

        # Place each tile
        for tile in self.tiles:
            x_pos = int(((tile.optimized_x or tile.x) - min_x) / downsample_factor)
            y_pos = int(((tile.optimized_y or tile.y) - min_y) / downsample_factor)

            # Downsample tile image if needed
            if downsample_factor > 1:
                tile_img = cv2.resize(tile.image,
                                    (tile.image.shape[1] // downsample_factor,
                                     tile.image.shape[0] // downsample_factor))
            else:
                tile_img = tile.image

            tile_h, tile_w = tile_img.shape[:2]

            # Calculate valid region within output bounds
            x_start = max(0, x_pos)
            y_start = max(0, y_pos)
            x_end = min(output_width, x_pos + tile_w)
            y_end = min(output_height, y_pos + tile_h)

            if x_end <= x_start or y_end <= y_start:
                continue

            # Calculate corresponding region in tile
            tile_x_start = x_start - x_pos
            tile_y_start = y_start - y_pos
            tile_x_end = tile_x_start + (x_end - x_start)
            tile_y_end = tile_y_start + (y_end - y_start)

            # Extract tile region
            if len(tile_img.shape) == 3:
                tile_region = tile_img[tile_y_start:tile_y_end, tile_x_start:tile_x_end].astype(np.float32)
            else:
                tile_region = tile_img[tile_y_start:tile_y_end, tile_x_start:tile_x_end].astype(np.float32)

            # Create weight map for blending
            weights = self._create_weight_map(tile_region.shape[:2], fusion_method)

            # Apply fusion method
            if fusion_method == 'multiband_blending':
                self._apply_multiband_blending(output_image, weight_map, tile_region, weights,
                                             x_start, y_start, x_end, y_end)
            else:
                # Standard blending
                if len(output_image.shape) == 3:
                    for c in range(3):
                        output_image[y_start:y_end, x_start:x_end, c] += tile_region[:, :, c] * weights
                else:
                    output_image[y_start:y_end, x_start:x_end] += tile_region * weights

                weight_map[y_start:y_end, x_start:x_end] += weights

        # Normalize by weights (only for non-multiband methods)
        if fusion_method != 'multiband_blending':
            weight_map[weight_map == 0] = 1  # Avoid division by zero
            if len(output_image.shape) == 3:
                for c in range(3):
                    output_image[:, :, c] /= weight_map
            else:
                output_image /= weight_map

        # Convert back to uint8
        output_image = np.clip(output_image, 0, 255).astype(np.uint8)

        # Save if path provided
        if output_path:
            success = cv2.imwrite(output_path, output_image)
            if success:
                logger.info(f"Saved stitched image to {output_path}")
            else:
                logger.error(f"Failed to save image to {output_path}")

        logger.info(f"Stitching completed in {time.time() - start_time:.2f}s")
        return output_image

    def _create_weight_map(self, shape, fusion_method):
        """Create weight map for different fusion methods"""
        h, w = shape

        if fusion_method == 'linear_blending':
            # Distance-based linear blending
            y_weights = np.minimum(np.arange(h) + 1, np.arange(h, 0, -1))
            x_weights = np.minimum(np.arange(w) + 1, np.arange(w, 0, -1))
            weights = np.outer(y_weights, x_weights).astype(np.float32)
            weights = np.minimum(weights, 50)  # Cap maximum weight

        elif fusion_method == 'multiband_blending':
            # Gaussian weight map for multiband blending
            center_y, center_x = h // 2, w // 2
            y, x = np.ogrid[:h, :w]

            # Create Gaussian-like weight map
            sigma_y, sigma_x = h * 0.3, w * 0.3
            weights = np.exp(-((y - center_y)**2 / (2 * sigma_y**2) +
                             (x - center_x)**2 / (2 * sigma_x**2)))
            weights = weights.astype(np.float32)

        elif fusion_method == 'exposure_compensation':
            # Edge-preserving weight map
            y_weights = np.minimum(np.arange(h) + 1, np.arange(h, 0, -1))
            x_weights = np.minimum(np.arange(w) + 1, np.arange(w, 0, -1))
            weights = np.outer(y_weights, x_weights).astype(np.float32)

            # Apply sigmoid function for smoother transitions
            weights = 1 / (1 + np.exp(-(weights - np.mean(weights)) / np.std(weights)))

        else:
            # Default uniform weights
            weights = np.ones((h, w), dtype=np.float32)

        return weights

    def _apply_multiband_blending(self, output_image, weight_map, tile_region, weights, x_start, y_start, x_end, y_end):
        """Apply multiband blending for seamless fusion"""
        if len(output_image.shape) == 3:
            # For color images, apply blending per channel
            for c in range(3):
                # Get existing content
                existing = output_image[y_start:y_end, x_start:x_end, c]
                existing_weights = weight_map[y_start:y_end, x_start:x_end]

                # Blend with new tile
                new_content = tile_region[:, :, c] * weights
                new_weights = weights

                # Normalize weights to avoid over-brightening
                total_weights = existing_weights + new_weights
                total_weights = np.maximum(total_weights, 1e-6)  # Avoid division by zero

                # Weighted average
                blended = (existing * existing_weights + new_content * new_weights) / total_weights

                # Update output
                output_image[y_start:y_end, x_start:x_end, c] = blended

            # Update weight map
            weight_map[y_start:y_end, x_start:x_end] += weights
        else:
            # For grayscale images
            existing = output_image[y_start:y_end, x_start:x_end]
            existing_weights = weight_map[y_start:y_end, x_start:x_end]

            new_content = tile_region * weights
            new_weights = weights

            total_weights = existing_weights + new_weights
            total_weights = np.maximum(total_weights, 1e-6)

            blended = (existing * existing_weights + new_content * new_weights) / total_weights
            output_image[y_start:y_end, x_start:x_end] = blended
            weight_map[y_start:y_end, x_start:x_end] += weights

    def stitch(self, image_dir: str, config_path: str = None, output_path: str = None,
              color_mode: str = 'color', downsample_factor: int = 1) -> np.ndarray:
        """Main stitching pipeline

        Args:
            image_dir: Directory containing images
            config_path: Path to TileConfiguration.txt
            output_path: Output image path
            color_mode: 'color' for RGB output, 'grayscale' for grayscale
            downsample_factor: Factor to downsample output (for speed)
        """
        logger.info(f"Starting image stitching pipeline in {color_mode} mode...")

        # Load configuration
        if config_path is None:
            config_path = os.path.join(image_dir, "TileConfiguration.txt")

        self.load_tile_configuration(config_path)

        # Load images in specified color mode
        self.load_images(image_dir, color_mode=color_mode)

        # Compute matches
        self.compute_all_matches()

        # Global optimization
        self.global_optimization()

        # Create final image with advanced fusion
        result = self.create_stitched_image(
            output_path,
            fusion_method='multiband_blending',
            downsample_factor=downsample_factor
        )

        return result

def main():
    """Example usage"""
    # Configuration
    image_dir = "Image_55"
    output_path = "stitched_result.jpg"

    # Create stitcher
    stitcher = FastImageStitcher(
        regression_threshold=0.3,
        max_displacement_threshold=2.5,
        absolute_displacement_threshold=3.5,
        subpixel_accuracy=True,
        num_threads=8
    )

    # Perform stitching
    try:
        result = stitcher.stitch(image_dir, output_path=output_path)
        logger.info(f"Stitching completed successfully! Result shape: {result.shape}")
    except Exception as e:
        logger.error(f"Stitching failed: {e}")
        raise

if __name__ == "__main__":
    main()
