#!/usr/bin/env python3
"""
Test script for color image stitching
"""

import cv2
import numpy as np
import os
import time
import logging
from python_stitching import FastImageStitcher

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_color_stitching():
    """Test color image stitching with the full dataset"""
    logger.info("Testing color image stitching...")
    
    image_dir = "Image_55"
    output_file = "color_stitched_result.jpg"
    
    # Check if directory exists
    if not os.path.exists(image_dir):
        logger.error(f"Directory '{image_dir}' not found!")
        return False
    
    # Check if TileConfiguration.txt exists
    config_file = os.path.join(image_dir, "TileConfiguration.txt")
    if not os.path.exists(config_file):
        logger.error(f"TileConfiguration.txt not found in '{image_dir}'!")
        return False
    
    print(f"🎨 Starting COLOR image stitching...")
    print(f"📁 Input directory: {image_dir}")
    print(f"💾 Output file: {output_file}")
    print("-" * 60)
    
    # Create stitcher with parameters optimized for color images
    stitcher = FastImageStitcher(
        regression_threshold=0.25,          # Slightly lower threshold for color
        max_displacement_threshold=3.0,     # Allow more displacement variation
        absolute_displacement_threshold=5.0, # Higher absolute threshold
        subpixel_accuracy=True,             # Enable subpixel accuracy
        num_threads=8                       # Use all available cores
    )
    
    try:
        start_time = time.time()
        
        # Perform color stitching
        result = stitcher.stitch(
            image_dir, 
            output_path=output_file,
            color_mode='color',              # 🌈 Enable COLOR mode
            downsample_factor=2              # Less downsampling for better quality
        )
        
        elapsed_time = time.time() - start_time
        
        print("-" * 60)
        print(f"✅ COLOR stitching completed successfully!")
        print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
        print(f"📐 Result dimensions: {result.shape[1]} x {result.shape[0]} pixels")
        print(f"🎨 Color channels: {result.shape[2] if len(result.shape) == 3 else 1}")
        print(f"🔗 Valid matches found: {len([m for m in stitcher.matches if m.valid])}")
        print(f"💾 Output saved to: {output_file}")
        
        # Calculate some statistics
        if stitcher.matches:
            correlations = [m.correlation for m in stitcher.matches if m.valid]
            if correlations:
                avg_correlation = sum(correlations) / len(correlations)
                max_correlation = max(correlations)
                print(f"📊 Average correlation: {avg_correlation:.4f}")
                print(f"📊 Maximum correlation: {max_correlation:.4f}")
        
        # Check if result is actually color
        if len(result.shape) == 3 and result.shape[2] == 3:
            print(f"🌈 SUCCESS: Output is COLOR image with {result.shape[2]} channels!")
        else:
            print(f"⚠️  WARNING: Output appears to be grayscale")
        
        return True
        
    except Exception as e:
        print(f"❌ Color stitching failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_vs_grayscale():
    """Compare color vs grayscale stitching"""
    logger.info("Comparing color vs grayscale stitching...")
    
    image_dir = "Image_55"
    
    # Test both modes
    modes = [
        {'name': 'Grayscale', 'color_mode': 'grayscale', 'output': 'grayscale_result.jpg'},
        {'name': 'Color', 'color_mode': 'color', 'output': 'color_result.jpg'}
    ]
    
    results = []
    
    for mode in modes:
        print(f"\n🔄 Testing {mode['name']} mode...")
        
        stitcher = FastImageStitcher(
            regression_threshold=0.3,
            subpixel_accuracy=True,
            num_threads=8
        )
        
        try:
            start_time = time.time()
            
            result = stitcher.stitch(
                image_dir,
                output_path=mode['output'],
                color_mode=mode['color_mode'],
                downsample_factor=4
            )
            
            elapsed_time = time.time() - start_time
            
            results.append({
                'mode': mode['name'],
                'time': elapsed_time,
                'shape': result.shape,
                'matches': len([m for m in stitcher.matches if m.valid]),
                'file': mode['output']
            })
            
            print(f"✅ {mode['name']}: {elapsed_time:.2f}s, shape {result.shape}")
            
        except Exception as e:
            print(f"❌ {mode['name']} failed: {e}")
            results.append({
                'mode': mode['name'],
                'error': str(e)
            })
    
    # Print comparison
    print("\n" + "="*60)
    print("📊 COMPARISON RESULTS")
    print("="*60)
    
    for result in results:
        if 'error' in result:
            print(f"{result['mode']}: FAILED - {result['error']}")
        else:
            channels = result['shape'][2] if len(result['shape']) == 3 else 1
            print(f"{result['mode']}: {result['time']:.2f}s, "
                  f"{result['matches']} matches, "
                  f"{channels} channel(s), "
                  f"saved to {result['file']}")

def check_sample_images():
    """Check if sample images are actually color"""
    logger.info("Checking sample images...")
    
    image_dir = "Image_55"
    sample_files = ["s_0001.jpg", "s_0002.jpg", "s_0030.jpg"]
    
    print("\n🔍 Checking sample images:")
    print("-" * 40)
    
    for filename in sample_files:
        filepath = os.path.join(image_dir, filename)
        if os.path.exists(filepath):
            img = cv2.imread(filepath, cv2.IMREAD_COLOR)
            if img is not None:
                print(f"📷 {filename}: {img.shape} - {'COLOR' if len(img.shape) == 3 else 'GRAYSCALE'}")
                
                # Check if image is actually color (not just 3-channel grayscale)
                if len(img.shape) == 3:
                    # Check if all channels are different
                    b, g, r = cv2.split(img)
                    if not (np.array_equal(b, g) and np.array_equal(g, r)):
                        print(f"   ✅ True color image (channels differ)")
                    else:
                        print(f"   ⚠️  Grayscale image stored as 3-channel")
            else:
                print(f"❌ {filename}: Could not load")
        else:
            print(f"❌ {filename}: File not found")

def main():
    """Run color stitching tests"""
    print("🌈 COLOR IMAGE STITCHING TEST")
    print("="*60)
    
    # Check sample images first
    check_sample_images()
    
    # Test color stitching
    print("\n" + "="*60)
    print("🎨 TESTING COLOR STITCHING")
    print("="*60)
    
    success = test_color_stitching()
    
    if success:
        print("\n" + "="*60)
        print("🔄 COMPARING MODES")
        print("="*60)
        test_color_vs_grayscale()
    
    print("\n" + "="*60)
    print("🏁 TESTING COMPLETED")
    print("="*60)

if __name__ == "__main__":
    main()
