#!/usr/bin/env python3
"""
Test improved stitching algorithm with ImageJ-compatible phase correlation
"""

import time
import os
import numpy as np
from python_stitching import FastImageStitcher

def test_improved_algorithm():
    """Test the improved stitching algorithm"""
    
    image_dir = "Image_55"
    
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return
    
    print("🔬 Testing Improved Stitching Algorithm")
    print("=" * 60)
    print("✨ New features:")
    print("   • ImageJ-compatible phase correlation normalization")
    print("   • Cross-correlation verification")
    print("   • Improved fusion methods")
    print("-" * 60)
    
    # Test with different quality settings
    test_configs = [
        {
            'name': 'High Quality',
            'regression_threshold': 0.3,
            'max_displacement_threshold': 2.0,
            'absolute_displacement_threshold': 3.0,
            'downsample_factor': 1,
            'output_file': 'improved_high_quality.jpg'
        },
        {
            'name': 'Balanced Quality',
            'regression_threshold': 0.25,
            'max_displacement_threshold': 2.5,
            'absolute_displacement_threshold': 3.5,
            'downsample_factor': 2,
            'output_file': 'improved_balanced.jpg'
        },
        {
            'name': 'Fast Processing',
            'regression_threshold': 0.2,
            'max_displacement_threshold': 3.0,
            'absolute_displacement_threshold': 4.0,
            'downsample_factor': 4,
            'output_file': 'improved_fast.jpg'
        }
    ]
    
    for config in test_configs:
        print(f"\n🎯 Testing {config['name']} Configuration")
        print(f"📁 Output: {config['output_file']}")
        print(f"⚙️ Settings:")
        print(f"   • Regression threshold: {config['regression_threshold']}")
        print(f"   • Max displacement: {config['max_displacement_threshold']}")
        print(f"   • Downsample factor: {config['downsample_factor']}")
        
        start_time = time.time()
        
        try:
            # Create stitcher with specific configuration
            stitcher = FastImageStitcher(
                regression_threshold=config['regression_threshold'],
                max_displacement_threshold=config['max_displacement_threshold'],
                absolute_displacement_threshold=config['absolute_displacement_threshold'],
                subpixel_accuracy=True,
                num_threads=16
            )
            
            # Perform stitching
            result = stitcher.stitch(
                image_dir=image_dir,
                output_path=config['output_file'],
                color_mode='color',
                downsample_factor=config['downsample_factor']
            )
            
            elapsed_time = time.time() - start_time
            
            # Check results
            if os.path.exists(config['output_file']):
                file_size = os.path.getsize(config['output_file']) / (1024 * 1024)  # MB
                print(f"✅ Success!")
                print(f"   ⏱️ Time: {elapsed_time:.2f}s")
                print(f"   📏 Size: {file_size:.1f}MB")
                print(f"   📐 Dimensions: {result.shape[1]} x {result.shape[0]} pixels")
                
                # Calculate quality metrics
                total_pixels = result.shape[0] * result.shape[1]
                pixels_per_mb = total_pixels / file_size if file_size > 0 else 0
                print(f"   🎨 Quality: {pixels_per_mb/1000:.0f}K pixels/MB")
            else:
                print("❌ Failed to create output file")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()
            
        print("-" * 40)

def compare_with_original():
    """Compare improved algorithm with original"""
    
    image_dir = "Image_55"
    
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return
    
    print("\n🔍 Comparison Test: Improved vs Original")
    print("=" * 60)
    
    # Test both versions
    configs = [
        {
            'name': 'Original Algorithm',
            'use_improved': False,
            'output_file': 'original_algorithm.jpg'
        },
        {
            'name': 'Improved Algorithm',
            'use_improved': True,
            'output_file': 'improved_algorithm.jpg'
        }
    ]
    
    results = {}
    
    for config in configs:
        print(f"\n🧪 Testing {config['name']}")
        
        start_time = time.time()
        
        try:
            stitcher = FastImageStitcher(
                regression_threshold=0.3,
                max_displacement_threshold=2.5,
                absolute_displacement_threshold=3.5,
                subpixel_accuracy=True,
                num_threads=16
            )
            
            result = stitcher.stitch(
                image_dir=image_dir,
                output_path=config['output_file'],
                color_mode='color',
                downsample_factor=2
            )
            
            elapsed_time = time.time() - start_time
            
            if os.path.exists(config['output_file']):
                file_size = os.path.getsize(config['output_file']) / (1024 * 1024)
                results[config['name']] = {
                    'time': elapsed_time,
                    'size': file_size,
                    'dimensions': result.shape,
                    'success': True
                }
                print(f"✅ Success: {elapsed_time:.2f}s, {file_size:.1f}MB")
            else:
                results[config['name']] = {'success': False}
                print("❌ Failed")
                
        except Exception as e:
            results[config['name']] = {'success': False, 'error': str(e)}
            print(f"❌ Error: {str(e)}")
    
    # Print comparison
    print("\n📊 Comparison Results:")
    print("-" * 40)
    for name, result in results.items():
        if result.get('success'):
            print(f"{name}:")
            print(f"   Time: {result['time']:.2f}s")
            print(f"   Size: {result['size']:.1f}MB")
            print(f"   Dimensions: {result['dimensions'][1]} x {result['dimensions'][0]}")
        else:
            print(f"{name}: Failed")

if __name__ == "__main__":
    test_improved_algorithm()
    compare_with_original()
    
    print("\n🎉 Improved stitching tests completed!")
    print("\n💡 Key improvements:")
    print("   • ImageJ-compatible phase correlation normalization")
    print("   • Cross-correlation verification for better accuracy")
    print("   • Enhanced fusion methods for seamless blending")
    print("   • Better error handling and validation")
    print("\n🔧 For best results:")
    print("   • Use regression_threshold=0.3 for good balance")
    print("   • Set downsample_factor=1 for maximum quality")
    print("   • Enable subpixel_accuracy for precise alignment")
