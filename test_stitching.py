#!/usr/bin/env python3
"""
Test script for the Python image stitching implementation
"""

import cv2
import numpy as np
import os
import time
import logging
from python_stitching import FastImageStitcher

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_small_subset():
    """Test with a small subset of images for quick validation"""
    logger.info("Testing with small subset of images...")
    
    # Create a subset directory with just a few images
    subset_dir = "Image_55_subset"
    if not os.path.exists(subset_dir):
        os.makedirs(subset_dir)
    
    # Copy first 9 images (3x3 grid) for testing
    import shutil
    
    # Create subset tile configuration
    subset_config = """# Define the number of dimensions we are working on
dim = 2

# Define the image coordinates (subset for testing)
s_0001.jpg ; ; (-0.000,0.000)
s_0002.jpg ; ; (2203.200,0.000)
s_0003.jpg ; ; (4406.400,0.000)
s_0030.jpg ; ; (-0.000,1843.200)
s_0029.jpg ; ; (2203.200,1843.200)
s_0028.jpg ; ; (4406.400,1843.200)
s_0031.jpg ; ; (-0.000,3686.400)
s_0032.jpg ; ; (2203.200,3686.400)
s_0033.jpg ; ; (4406.400,3686.400)
"""
    
    # Write subset configuration
    with open(os.path.join(subset_dir, "TileConfiguration.txt"), 'w') as f:
        f.write(subset_config)
    
    # Copy images
    image_files = ["s_0001.jpg", "s_0002.jpg", "s_0003.jpg", 
                   "s_0028.jpg", "s_0029.jpg", "s_0030.jpg",
                   "s_0031.jpg", "s_0032.jpg", "s_0033.jpg"]
    
    for img_file in image_files:
        src = os.path.join("Image_55", img_file)
        dst = os.path.join(subset_dir, img_file)
        if os.path.exists(src) and not os.path.exists(dst):
            shutil.copy2(src, dst)
    
    # Test stitching
    stitcher = FastImageStitcher(
        regression_threshold=0.2,  # Lower threshold for testing
        max_displacement_threshold=3.0,
        absolute_displacement_threshold=5.0,
        subpixel_accuracy=True,
        num_threads=4
    )
    
    try:
        result = stitcher.stitch(subset_dir, output_path="test_result_subset.jpg")
        logger.info(f"Subset test completed! Result shape: {result.shape}")
        return True
    except Exception as e:
        logger.error(f"Subset test failed: {e}")
        return False

def test_full_dataset():
    """Test with the full dataset"""
    logger.info("Testing with full dataset...")
    
    stitcher = FastImageStitcher(
        regression_threshold=0.3,
        max_displacement_threshold=2.5,
        absolute_displacement_threshold=3.5,
        subpixel_accuracy=True,
        num_threads=8
    )
    
    try:
        start_time = time.time()
        result = stitcher.stitch("Image_55", output_path="test_result_full.jpg")
        elapsed = time.time() - start_time
        
        logger.info(f"Full test completed in {elapsed:.2f}s! Result shape: {result.shape}")
        return True
    except Exception as e:
        logger.error(f"Full test failed: {e}")
        return False

def benchmark_comparison():
    """Compare performance with different settings"""
    logger.info("Running benchmark comparison...")
    
    configs = [
        {"name": "Fast", "subpixel": False, "threads": 8, "threshold": 0.2},
        {"name": "Accurate", "subpixel": True, "threads": 8, "threshold": 0.3},
        {"name": "Conservative", "subpixel": True, "threads": 4, "threshold": 0.4},
    ]
    
    results = []
    
    for config in configs:
        logger.info(f"Testing configuration: {config['name']}")
        
        stitcher = FastImageStitcher(
            regression_threshold=config["threshold"],
            max_displacement_threshold=2.5,
            absolute_displacement_threshold=3.5,
            subpixel_accuracy=config["subpixel"],
            num_threads=config["threads"]
        )
        
        try:
            start_time = time.time()
            
            # Test with subset for speed
            result = stitcher.stitch("Image_55_subset", 
                                   output_path=f"benchmark_{config['name'].lower()}.jpg")
            
            elapsed = time.time() - start_time
            
            results.append({
                "config": config["name"],
                "time": elapsed,
                "shape": result.shape,
                "matches": len(stitcher.matches)
            })
            
            logger.info(f"{config['name']}: {elapsed:.2f}s, {len(stitcher.matches)} matches")
            
        except Exception as e:
            logger.error(f"Configuration {config['name']} failed: {e}")
            results.append({
                "config": config["name"],
                "time": -1,
                "error": str(e)
            })
    
    # Print summary
    logger.info("\n=== Benchmark Results ===")
    for result in results:
        if "error" in result:
            logger.info(f"{result['config']}: FAILED - {result['error']}")
        else:
            logger.info(f"{result['config']}: {result['time']:.2f}s, "
                       f"{result['matches']} matches, shape {result['shape']}")

def validate_against_imagej():
    """Compare results with ImageJ output if available"""
    logger.info("Validating against ImageJ results...")
    
    # Check if ImageJ result exists
    imagej_result_path = "Image_55.jpg"  # Expected ImageJ output
    
    if not os.path.exists(imagej_result_path):
        logger.warning("ImageJ result not found, skipping validation")
        return
    
    # Load ImageJ result
    imagej_result = cv2.imread(imagej_result_path)
    if imagej_result is None:
        logger.warning("Could not load ImageJ result")
        return
    
    # Generate our result
    stitcher = FastImageStitcher()
    our_result = stitcher.stitch("Image_55", output_path="validation_result.jpg")
    
    # Compare dimensions
    logger.info(f"ImageJ result shape: {imagej_result.shape}")
    logger.info(f"Our result shape: {our_result.shape}")
    
    # Calculate similarity metrics if dimensions are compatible
    if len(our_result.shape) == len(imagej_result.shape):
        # Resize to same dimensions for comparison
        min_h = min(imagej_result.shape[0], our_result.shape[0])
        min_w = min(imagej_result.shape[1], our_result.shape[1])
        
        imagej_resized = cv2.resize(imagej_result, (min_w, min_h))
        our_resized = cv2.resize(our_result, (min_w, min_h))
        
        # Convert to grayscale for comparison
        if len(imagej_resized.shape) == 3:
            imagej_gray = cv2.cvtColor(imagej_resized, cv2.COLOR_BGR2GRAY)
        else:
            imagej_gray = imagej_resized
            
        if len(our_resized.shape) == 3:
            our_gray = cv2.cvtColor(our_resized, cv2.COLOR_BGR2GRAY)
        else:
            our_gray = our_resized
        
        # Calculate SSIM or correlation
        correlation = cv2.matchTemplate(imagej_gray, our_gray, cv2.TM_CCOEFF_NORMED)[0, 0]
        logger.info(f"Correlation with ImageJ result: {correlation:.4f}")

def main():
    """Run all tests"""
    logger.info("Starting Python stitching tests...")
    
    # Test 1: Small subset
    logger.info("\n=== Test 1: Small Subset ===")
    subset_success = test_small_subset()
    
    if subset_success:
        # Test 2: Benchmark different configurations
        logger.info("\n=== Test 2: Benchmark ===")
        benchmark_comparison()
        
        # Test 3: Full dataset (optional)
        logger.info("\n=== Test 3: Full Dataset ===")
        response = input("Run full dataset test? (y/n): ").lower().strip()
        if response == 'y':
            test_full_dataset()
        
        # Test 4: Validation against ImageJ
        logger.info("\n=== Test 4: Validation ===")
        validate_against_imagej()
    
    logger.info("Testing completed!")

if __name__ == "__main__":
    main()
