#!/usr/bin/env python3
"""
Test ImageJ-style threading implementation
"""

import time
import os
import numpy as np
from python_stitching import FastImageStitcher

def test_threading_modes():
    """Test different threading modes"""
    
    image_dir = "Image_55"
    
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return
    
    print("🧵 Testing ImageJ-Style Threading Implementation")
    print("=" * 60)
    
    # Test configurations
    test_configs = [
        {
            'name': 'Single-Thread Mode (Save Memory)',
            'cpu_mem_choice': 0,
            'output_file': 'single_thread_result.jpg',
            'description': 'ImageJ cpuMemChoice=0 equivalent'
        },
        {
            'name': 'Multi-Thread Mode (Save Time)',
            'cpu_mem_choice': 1,
            'output_file': 'multi_thread_result.jpg',
            'description': 'ImageJ cpuMemChoice=1 equivalent'
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🎯 Testing {config['name']}")
        print(f"📝 Description: {config['description']}")
        print(f"📁 Output: {config['output_file']}")
        
        start_time = time.time()
        
        try:
            # Create stitcher with specific threading mode
            stitcher = FastImageStitcher(
                regression_threshold=0.3,
                max_displacement_threshold=2.5,
                absolute_displacement_threshold=3.5,
                subpixel_accuracy=True,
                cpu_mem_choice=config['cpu_mem_choice']
            )
            
            print(f"⚙️ Threading mode: {stitcher.cpu_mem_choice}")
            print(f"🧵 Number of threads: {stitcher.num_threads}")
            
            # Perform stitching
            result = stitcher.stitch(
                image_dir=image_dir,
                output_path=config['output_file'],
                color_mode='color',
                downsample_factor=2  # Use 2 for faster testing
            )
            
            elapsed_time = time.time() - start_time
            
            # Check results
            if os.path.exists(config['output_file']):
                file_size = os.path.getsize(config['output_file']) / (1024 * 1024)  # MB
                
                results[config['name']] = {
                    'time': elapsed_time,
                    'size': file_size,
                    'dimensions': result.shape,
                    'threads': stitcher.num_threads,
                    'matches': len(stitcher.matches),
                    'success': True
                }
                
                print(f"✅ Success!")
                print(f"   ⏱️ Time: {elapsed_time:.2f}s")
                print(f"   📏 Size: {file_size:.1f}MB")
                print(f"   📐 Dimensions: {result.shape[1]} x {result.shape[0]} pixels")
                print(f"   🔗 Valid matches: {len(stitcher.matches)}")
            else:
                results[config['name']] = {'success': False}
                print("❌ Failed to create output file")
                
        except Exception as e:
            results[config['name']] = {'success': False, 'error': str(e)}
            print(f"❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()
            
        print("-" * 50)
    
    # Print comparison
    print("\n📊 Threading Performance Comparison")
    print("=" * 60)
    
    successful_results = {k: v for k, v in results.items() if v.get('success')}
    
    if len(successful_results) >= 2:
        single_thread = None
        multi_thread = None
        
        for name, result in successful_results.items():
            if result['threads'] == 1:
                single_thread = result
                single_name = name
            else:
                multi_thread = result
                multi_name = name
        
        if single_thread and multi_thread:
            speedup = single_thread['time'] / multi_thread['time']
            
            print(f"\n🏃 Performance Analysis:")
            print(f"   Single-thread time: {single_thread['time']:.2f}s")
            print(f"   Multi-thread time:  {multi_thread['time']:.2f}s")
            print(f"   Speedup factor:     {speedup:.2f}x")
            print(f"   Efficiency:         {speedup/multi_thread['threads']*100:.1f}%")
            
            print(f"\n🔍 Quality Comparison:")
            print(f"   Single-thread matches: {single_thread['matches']}")
            print(f"   Multi-thread matches:  {multi_thread['matches']}")
            
            if single_thread['matches'] == multi_thread['matches']:
                print("   ✅ Same number of matches - threading is deterministic")
            else:
                print("   ⚠️ Different matches - may indicate threading issues")
    
    # Print detailed results
    print(f"\n📋 Detailed Results:")
    for name, result in results.items():
        print(f"\n{name}:")
        if result.get('success'):
            print(f"   Time: {result['time']:.2f}s")
            print(f"   Size: {result['size']:.1f}MB")
            print(f"   Dimensions: {result['dimensions'][1]} x {result['dimensions'][0]}")
            print(f"   Threads: {result['threads']}")
            print(f"   Matches: {result['matches']}")
        else:
            error_msg = result.get('error', 'Unknown error')
            print(f"   Status: Failed - {error_msg}")

def test_thread_scaling():
    """Test performance with different thread counts"""
    
    image_dir = "Image_55"
    
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return
    
    print("\n🔬 Thread Scaling Analysis")
    print("=" * 60)
    
    # Test different thread counts
    thread_counts = [1, 2, 4, 8, os.cpu_count()]
    thread_counts = sorted(list(set(thread_counts)))  # Remove duplicates and sort
    
    results = {}
    
    for num_threads in thread_counts:
        print(f"\n🧵 Testing with {num_threads} threads...")
        
        start_time = time.time()
        
        try:
            stitcher = FastImageStitcher(
                regression_threshold=0.3,
                max_displacement_threshold=2.5,
                absolute_displacement_threshold=3.5,
                subpixel_accuracy=True,
                num_threads=num_threads,
                cpu_mem_choice=1  # Multi-thread mode
            )
            
            # Only compute matches (faster for testing)
            stitcher.load_tile_configuration(os.path.join(image_dir, "TileConfiguration.txt"))
            stitcher.load_images(image_dir, color_mode='color')
            stitcher.compute_all_matches()
            
            elapsed_time = time.time() - start_time
            
            results[num_threads] = {
                'time': elapsed_time,
                'matches': len(stitcher.matches),
                'success': True
            }
            
            print(f"   ⏱️ Time: {elapsed_time:.2f}s")
            print(f"   🔗 Matches: {len(stitcher.matches)}")
            
        except Exception as e:
            results[num_threads] = {'success': False, 'error': str(e)}
            print(f"   ❌ Error: {str(e)}")
    
    # Analyze scaling
    successful_results = {k: v for k, v in results.items() if v.get('success')}
    
    if len(successful_results) > 1:
        print(f"\n📈 Scaling Analysis:")
        baseline_time = successful_results[min(successful_results.keys())]['time']
        
        for threads, result in sorted(successful_results.items()):
            speedup = baseline_time / result['time']
            efficiency = speedup / threads * 100
            
            print(f"   {threads:2d} threads: {result['time']:6.2f}s, "
                  f"speedup: {speedup:4.2f}x, efficiency: {efficiency:5.1f}%")

if __name__ == "__main__":
    test_threading_modes()
    test_thread_scaling()
    
    print("\n🎉 Threading tests completed!")
    print("\n💡 Key findings:")
    print("   • ImageJ-style threading implemented with cpu_mem_choice parameter")
    print("   • Single-thread mode (cpu_mem_choice=0) saves memory")
    print("   • Multi-thread mode (cpu_mem_choice=1) saves computation time")
    print("   • Work distribution uses modulo operation like ImageJ")
    print("   • Thread scaling efficiency depends on workload parallelizability")
