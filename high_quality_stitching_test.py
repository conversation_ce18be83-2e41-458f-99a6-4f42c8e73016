#!/usr/bin/env python3
"""
High-quality image stitching test with advanced fusion methods
"""

import time
import os
import numpy as np
from python_stitching import FastImageStitcher

def test_high_quality_stitching():
    """Test high-quality stitching with different fusion methods"""
    
    image_dir = "Image_55"
    
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return
    
    # Test different fusion methods
    fusion_methods = [
        ('multiband_blending', 'high_quality_multiband.jpg'),
        ('exposure_compensation', 'high_quality_exposure.jpg'),
        ('linear_blending', 'high_quality_linear.jpg')
    ]
    
    # Create high-quality stitcher
    stitcher = FastImageStitcher(
        regression_threshold=0.2,           # Very strict threshold
        max_displacement_threshold=1.5,     # Very strict error threshold
        absolute_displacement_threshold=2.5,
        subpixel_accuracy=True,
        num_threads=16
    )
    
    print("🔬 High-Quality Image Stitching Test")
    print("=" * 50)
    
    for fusion_method, output_file in fusion_methods:
        print(f"\n🎨 Testing {fusion_method}...")
        print(f"📁 Output: {output_file}")
        
        start_time = time.time()
        
        try:
            # Load configuration and images
            stitcher.load_tile_configuration(os.path.join(image_dir, "TileConfiguration.txt"))
            stitcher.load_images(image_dir, color_mode='color')
            
            # Compute matches
            stitcher.compute_pairwise_matches()
            stitcher.global_optimization()
            
            # Create high-quality stitched image
            result = stitcher.create_stitched_image(
                output_path=output_file,
                fusion_method=fusion_method,
                downsample_factor=1  # Full resolution
            )
            
            elapsed_time = time.time() - start_time
            
            # Get file size
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
                print(f"✅ Success! Time: {elapsed_time:.2f}s, Size: {file_size:.1f}MB")
                print(f"📐 Dimensions: {result.shape[1]} x {result.shape[0]} pixels")
            else:
                print("❌ Failed to create output file")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            
        print("-" * 30)

def test_quality_comparison():
    """Compare different quality settings"""
    
    image_dir = "Image_55"
    
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return
    
    print("\n🔍 Quality Comparison Test")
    print("=" * 50)
    
    # Test different downsample factors
    quality_settings = [
        (1, 'ultra_high_quality.jpg', 'Ultra High Quality (Full Resolution)'),
        (2, 'high_quality.jpg', 'High Quality (50% Resolution)'),
        (4, 'standard_quality.jpg', 'Standard Quality (25% Resolution)')
    ]
    
    stitcher = FastImageStitcher(
        regression_threshold=0.2,
        max_displacement_threshold=1.5,
        absolute_displacement_threshold=2.5,
        subpixel_accuracy=True,
        num_threads=16
    )
    
    for downsample_factor, output_file, description in quality_settings:
        print(f"\n🎯 {description}")
        print(f"📁 Output: {output_file}")
        
        start_time = time.time()
        
        try:
            result = stitcher.stitch(
                image_dir=image_dir,
                output_path=output_file,
                color_mode='color',
                downsample_factor=downsample_factor
            )
            
            elapsed_time = time.time() - start_time
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
                print(f"✅ Success! Time: {elapsed_time:.2f}s, Size: {file_size:.1f}MB")
                print(f"📐 Dimensions: {result.shape[1]} x {result.shape[0]} pixels")
                
                # Calculate expected vs actual file size
                expected_pixels = result.shape[0] * result.shape[1] * 3
                compression_ratio = (expected_pixels * 3) / (file_size * 1024 * 1024)
                print(f"🗜️ Compression ratio: {compression_ratio:.1f}:1")
            else:
                print("❌ Failed to create output file")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            
        print("-" * 30)

if __name__ == "__main__":
    test_high_quality_stitching()
    test_quality_comparison()
    
    print("\n🎉 High-quality stitching tests completed!")
    print("\n💡 Tips for best results:")
    print("   • Use downsample_factor=1 for maximum quality")
    print("   • multiband_blending provides the most seamless fusion")
    print("   • Lower regression_threshold gives better matches but slower processing")
    print("   • Expected file sizes: 100-300MB for full resolution color images")
