#!/usr/bin/env python3
"""
Simple script to stitch images using the FastImageStitcher
Usage: python stitch_images.py [image_directory] [output_file]
"""

import sys
import os
import time
from python_stitching import FastImageStitcher

def main():
    # Default parameters
    image_dir = "Image_55"
    output_file = "stitched_result.jpg"
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        image_dir = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    # Check if directory exists
    if not os.path.exists(image_dir):
        print(f"Error: Directory '{image_dir}' not found!")
        return 1
    
    # Check if TileConfiguration.txt exists
    config_file = os.path.join(image_dir, "TileConfiguration.txt")
    if not os.path.exists(config_file):
        print(f"Error: TileConfiguration.txt not found in '{image_dir}'!")
        return 1
    
    print(f"Starting image stitching...")
    print(f"Input directory: {image_dir}")
    print(f"Output file: {output_file}")
    print("-" * 50)
    
    # Create stitcher with high-quality parameters
    stitcher = FastImageStitcher(
        regression_threshold=0.25,          # Lower threshold for better matches
        max_displacement_threshold=2.0,     # Stricter error threshold
        absolute_displacement_threshold=3.0, # Stricter absolute threshold
        subpixel_accuracy=True,             # Enable subpixel accuracy
        num_threads=16                      # Number of parallel threads
    )

    try:
        start_time = time.time()

        # Perform stitching in high-quality color mode
        result = stitcher.stitch(
            image_dir,
            output_path=output_file,
            color_mode='color',              # Enable color output
            downsample_factor=1              # Full resolution for maximum quality
        )

        elapsed_time = time.time() - start_time
        
        print("-" * 50)
        print(f"✅ Stitching completed successfully!")
        print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
        print(f"📐 Result dimensions: {result.shape[1]} x {result.shape[0]} pixels")
        print(f"🔗 Valid matches found: {len([m for m in stitcher.matches if m.valid])}")
        print(f"💾 Output saved to: {output_file}")
        
        # Calculate some statistics
        if stitcher.matches:
            correlations = [m.correlation for m in stitcher.matches if m.valid]
            if correlations:
                avg_correlation = sum(correlations) / len(correlations)
                print(f"📊 Average correlation: {avg_correlation:.4f}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Stitching failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
